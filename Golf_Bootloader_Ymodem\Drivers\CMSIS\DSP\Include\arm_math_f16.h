/******************************************************************************
 * @file     arm_math_f16.h
 * @brief    Public header file for f16 function of the CMSIS DSP Library
 * @version  V1.10.0
 * @date     08 July 2021
 * Target Processor: Cortex-M and Cortex-A cores
 ******************************************************************************/
/*
 * Copyright (c) 2010-2021 Arm Limited or its affiliates. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef _ARM_MATH_F16_H
#define _ARM_MATH_F16_H

#include "arm_math.h"

#ifdef   __cplusplus
extern "C"
{
#endif

#include "arm_math_types_f16.h"
#include "dsp/none.h"
#include "dsp/utils.h"
#include "dsp/basic_math_functions_f16.h"
#include "dsp/interpolation_functions_f16.h"
#include "dsp/bayes_functions_f16.h"
#include "dsp/matrix_functions_f16.h"
#include "dsp/complex_math_functions_f16.h"
#include "dsp/statistics_functions_f16.h"
#include "dsp/controller_functions_f16.h"
#include "dsp/support_functions_f16.h"
#include "dsp/distance_functions_f16.h"
#include "dsp/svm_functions_f16.h"
#include "dsp/fast_math_functions_f16.h"
#include "dsp/transform_functions_f16.h"
#include "dsp/filtering_functions_f16.h"

#ifdef   __cplusplus
}
#endif

#endif /* _ARM_MATH_F16_H */


